import { forwardRef, useState, useCallback, useImperative<PERSON>andle } from 'react';
import { Modal, Form, message } from 'antd';
import { useNavigate } from 'umi';
import { getCaseTree } from 'COMMON/api/front_qe_tools/node';
import { connectModel } from 'COMMON/middleware';
import { filterConditionCaseNodeRecursive } from 'COMMON/utils/commonUtils';
import baseModel from 'COMMON/models/baseModel';
import NewCaseSelect from './NewCaseSelect';
import { concat, isEmpty } from 'lodash';

function CaseNodeTree(props, ref) {
    const {
        caseNodeList,
        setCaseNodeList = () => {},
        modalType = 'create',
        filterType = [],
        setCaseNodeKeys
    } = props;
    const [open, setOpen] = useState(false);
    const [curNodeId, setCurNodeId] = useState(null);
    const [curCaseRootId, setCurCaseRootId] = useState(null);
    const [caseTree, setCaseTree] = useState([]);
    const [curOsType, setCurOsType] = useState(1);
    const [form] = Form.useForm();
    const [filterData, setFilterData] = useState({});
    const [messageApi, contextHolder] = message.useMessage();

    const getLeafNodeIds = (tree, keys = []) => {
        tree.map((ele) => {
            if (ele.children.length === 0) {
                keys.push(ele.caseNodeId);
            }
            getLeafNodeIds(ele.children, keys);
        });
        return keys;
    };
    const showModal = (
        caseRootId,
        osType,
        nodeId,
        selectedCaseNodeList = [],
        initFilterData = {}
    ) => {
        const innerCaseNodeList = isEmpty(selectedCaseNodeList)
            ? caseNodeList
            : selectedCaseNodeList;
        getCaseTree({ caseRootId: caseRootId, withSign: false }).then((res) => {
            setCaseTree(res);
            setCurCaseRootId(caseRootId);
            setCurNodeId(nodeId);
            setCurOsType(osType);
            // 新增情况下默认全不选，编辑情况根据传入的值
            let keys = [];
            // 获取所有叶子结点
            let newCaseNodeList = [...innerCaseNodeList];
            let curNodeListIndex = newCaseNodeList.findIndex(
                (item) => item.nodeId === nodeId && item.osType === osType
            );
            let curNodeList = newCaseNodeList[curNodeListIndex];

            // 如果已经有选中的用例，使用已有的选中状态
            if (curNodeList && curNodeList.caseIdList && curNodeList.caseIdList.length > 0) {
                keys = curNodeList.caseIdList;
            }
            if (!isEmpty(initFilterData)) {
                setFilterData(initFilterData);
                // 筛选一下
                let _caseNodeWithCondition = filterConditionCaseNodeRecursive([res], {
                    search: '',
                    ...initFilterData,
                    stampInfo: {
                        osType // 端类型
                    }
                });
                let childrenKeys = getLeafNodeIds(_caseNodeWithCondition ?? []);
                keys = childrenKeys ?? [];
            }
            if (setCaseNodeKeys && !isEmpty(initFilterData)) {
                setCaseNodeKeys(keys, `${nodeId}-${osType}`);
            }
            if (!curNodeList || !curNodeList?.caseIdList || !curNodeList?.caseIdList?.length) {
                if (!curNodeList) {
                    newCaseNodeList.push({
                        nodeId: nodeId,
                        caseRootId: caseRootId,
                        osType: osType,
                        caseIdList: keys
                    });
                } else {
                    newCaseNodeList[curNodeListIndex].caseIdList = keys;
                }
                setCaseNodeList(newCaseNodeList, true);
            }
            setOpen(true);
        });
    };

    const hideModal = useCallback(() => {
        form.resetFields();
        setOpen(false);
        setFilterData({});
    }, []);

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(
        ref,
        () => {
            return {
                show: showModal
            };
        },
        [showModal]
    );

    return (
        <>
            {contextHolder}
            <Modal
                title="测试用例选择"
                open={open}
                transitionName=""
                destroyOnClose
                onCancel={hideModal}
                width={window.innerWidth * 0.5}
                mask="true"
                maskClosable="false"
                footer={null}
                zIndex={1020}
            >
                <br />
                <NewCaseSelect
                    initFilterData={filterData}
                    curNodeId={curNodeId}
                    curCaseRootId={curCaseRootId}
                    dataSource={caseTree === null ? [] : [caseTree]}
                    curOsType={curOsType}
                    caseNodeList={caseNodeList}
                    setCaseNodeList={setCaseNodeList}
                    disabled={modalType === 'view'}
                    filterType={filterType}
                />
            </Modal>
        </>
    );
}

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    creationConfig: state.common.base.creationConfig
}))(forwardRef(CaseNodeTree));
