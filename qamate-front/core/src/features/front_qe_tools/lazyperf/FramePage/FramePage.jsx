import { useEffect, useState } from 'react';
import { getSpeedRoundList } from 'COMMON/api/front_qe_tools/lazyperf';
import CalibrateTable from './components/CalibrateTable';
import Operate from '../components/Operate';
import styles from './FramePage.module.less';

const FramePage = (props) => {
    const { curTask } = props;
    const [curCaseNodeId, setCurCaseNodeId] = useState(null);
    const [curSceneId, setCurSceneId] = useState(null);
    const [sceneList, setSceneList] = useState([]);
    const [recordList, setRecordList] = useState([]);


    const caseNodeOptions = (curTask?.planParams?.caseNodeParams?.caseNodeList || []).map(
        (item) => ({
            value: item?.caseNodeId,
            key: item?.caseNodeId,
            label: item?.caseNodeName
        })
    );

    const sceneListOptions = (sceneList || []).map((item) => ({
        value: item?.id,
        key: item?.id,
        label: item?.name
    }));

    useEffect(() => {
        const curCaseNode = curTask?.planParams?.caseNodeParams?.caseNodeList?.find(
            (item) => item?.caseNodeId === curCaseNodeId
        );
        setSceneList(curCaseNode?.sceneList || []);
    }, [curCaseNodeId]);

    useEffect(() => {
        async function func() {
            if (!curTask) {
                return;
            }
            let res = await getSpeedRoundList({
                planId: curTask?.planId,
                caseNodeId: curCaseNodeId,
                // sceneId: curSceneId
            });
            setRecordList(res || []);
        }
        func();
    }, [curSceneId, curCaseNodeId]);

    console.log('curSceneId', curSceneId);

    // Mock数据 - 模拟真实的性能测试场景

    return (
        <>
            <div className={styles.taskPage}>
                <Operate
                    planId={curTask?.planId}
                    caseNodeOptions={caseNodeOptions}
                    setCurCaseNodeId={setCurCaseNodeId}
                    setCurSceneId={setCurSceneId}
                    sceneListOptions={sceneListOptions}
                    recordList={recordList}
                />
                {/* <CalibrateTable recordList={recordList} /> */}
            </div>
        </>
    );
};

export default FramePage;
