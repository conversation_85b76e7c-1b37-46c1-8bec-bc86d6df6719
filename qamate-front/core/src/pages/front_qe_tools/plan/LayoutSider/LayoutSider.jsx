import { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>, Spin, Badge, message } from 'antd';
import { useNavigate, useLocation } from 'umi';
import { isEmpty } from 'lodash';
import { stringifyUrl } from 'query-string';
import classnames from 'classnames';
import { RedoOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import planModel from 'COMMON/models/planModel';
import Search from 'COMMON/components/Search';
import { getQueryParams } from 'COMMON/utils/utils';
import {
    getPlanList,
    getPlanDetail,
    getServerPlanTemplateList
} from 'COMMON/api/front_qe_tools/plan/plan';
import { queryDailyTaskList, queryDailyTaskDetail } from 'COMMON/api/front_qe_tools/plan/daily';
import {
    queryStabilityPlanList,
    getStabilityDetail,
    getStabilityResume,
    queryStabilityPlanFilter
} from 'COMMON/api/front_qe_tools/plan/stability';
import EventBus from 'COMMON/utils/eventBus';
import GroupSelect from './Group/GroupSelect';
import DirectoryPlanList from './PlanList/DirectoryPlanList';
import DailyDirectoryPlanList from './PlanList/DailyDirectoryPlanList';
import StabilityDirectoryPlanList from './PlanList/StabilityDirectoryPlanList';
import ApiDirectoryPlanList from './PlanList/ApiDirectoryPlanList';
import StabilityFilter from './StabilityFilter';
import styles from './LayoutSider.module.less';

function LayoutSider(props) {
    const {
        currentPageForPlanList,
        currentSpace,
        currentPlan,
        setCurrentPlan,
        setPlanList,
        searchValue,
        setSearchValue,
        currentPlanGroup,
        planGroupList,
        currentModule,
        planTemplateList,
        setPlanTemplateList
    } = props;
    const [showSearch, setShowSearch] = useState(false);
    const [openKeys, setOpenKeys] = useState([]);
    const [loading, setLoading] = useState(false);
    const stabilityDirectoryRef = useRef();
    const navigate = useNavigate();
    const location = useLocation();
    const query = getQueryParams();

    const handleSearchClick = (value) => {
        // 设置搜索 不清空筛选结果
        setSearchValue(value);
        // 如果搜索框为空 重新加载数据
        if (!value) {
            EventBus.emit('searchValueCleared');
        } else {
            EventBus.emit('searchValueChanged', value);
        }
        setOpenKeys(currentPlan?.id ? [currentPlan?.id] : []);
    };

    const handleFilter = async (filterParams) => {
        try {
            setLoading(true);
            // 判断是否是刷新操作
            if (filterParams.isRefresh === true) {
                // 刷新操作 - 清空所有条件包括搜索词
                setSearchValue('');
                const { isRefresh, ...params } = filterParams;
                const res = await queryStabilityPlanList(params);
                setPlanList(res?.planPipeList?.flatMap((item) => item?.planList || []) || []);
                // 刷新
                if (stabilityDirectoryRef.current) {
                    stabilityDirectoryRef.current.handleRefresh();
                }
            } else {
                // 筛选操作
                const res = await queryStabilityPlanFilter(filterParams);
                if (isEmpty(res)) {
                    setPlanList([]);
                    // 筛选
                    if (stabilityDirectoryRef.current) {
                        stabilityDirectoryRef.current.handleFilterApplied([]);
                    }
                    return;
                }
                const planItems = res?.planPipeList?.flatMap((item) => item?.planList || []) || [];
                setPlanList(planItems);
                // 筛选
                if (stabilityDirectoryRef.current) {
                    stabilityDirectoryRef.current.handleFilterApplied(res?.planPipeList || []);
                }
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <div className={styles.layoutSider}>
                <div className={styles.siderHeader}>
                    <div className={styles.siderHeader}>
                        {!showSearch && (
                            <span className={styles.groupInfo}>
                                <GroupSelect planGroupList={planGroupList} />
                            </span>
                        )}
                        {showSearch && (
                            <Search
                                className={styles.searchStyle}
                                autoFocus={true}
                                value={searchValue}
                                onSearch={handleSearchClick}
                                onBlur={() => {
                                    setShowSearch(false);
                                }}
                            />
                        )}
                        <div className={styles.iconGroup}>
                            {!showSearch && (
                                <Tooltip title={'搜索'}>
                                    <div
                                        className={classnames(styles.iconWrapper)}
                                        onClick={() => {
                                            setShowSearch(true);
                                        }}
                                    >
                                        <Badge dot={!isEmpty(searchValue)}>
                                            <SearchOutlined className={styles.addIcon} />
                                        </Badge>
                                    </div>
                                </Tooltip>
                            )}
                            {location.pathname.includes('stability') && (
                                <div className={classnames(styles.iconWrapper)}>
                                    <span className={styles.addIcon}>
                                        <StabilityFilter
                                            onFilter={handleFilter}
                                            currentSpace={currentSpace}
                                        />
                                    </span>
                                </div>
                            )}
                            <div className={classnames(styles.iconWrapper)}>
                                <span className={styles.addIcon}>
                                    <RedoOutlined
                                        onClick={async () => {
                                            try {
                                                if (
                                                    currentPlan?.planId ||
                                                    currentPlan?.id ||
                                                    location.pathname.includes('api')
                                                ) {
                                                    setLoading(true);
                                                    let planList = [];
                                                    let planInfo;
                                                    if (location.pathname.includes('daily')) {
                                                        const res = await queryDailyTaskList({
                                                            moduleId: currentSpace?.id
                                                        });
                                                        planList = res?.planList;
                                                        planInfo = await queryDailyTaskDetail({
                                                            planId: +query?.planId
                                                        });
                                                        setCurrentPlan({
                                                            id: currentPlan?.planId,
                                                            planId: currentPlan?.planId,
                                                            ...planInfo
                                                        });
                                                    } else if (
                                                        location.pathname.includes('stability')
                                                    ) {
                                                        const res = await queryStabilityPlanList({
                                                            moduleId: currentSpace?.id,
                                                            // moduleId: 111,
                                                            pageSize: parseInt(
                                                                (window.innerHeight - 110) / 30 - 1,
                                                                10
                                                            ),
                                                            pageIndex: currentPageForPlanList || 1
                                                        });
                                                        setPlanList(
                                                            res?.planPipeList?.flatMap(
                                                                (item) => item.planList
                                                            ) || []
                                                        );
                                                        if (+query?.planId) {
                                                            planInfo = await getStabilityDetail({
                                                                planId: +query?.planId
                                                            });
                                                            const planResume =
                                                                await getStabilityResume({
                                                                    planId: +query?.planId
                                                                });
                                                            setCurrentPlan({
                                                                id: currentPlan?.planId,
                                                                planId: currentPlan?.planId,
                                                                ...planInfo,
                                                                ...planResume
                                                            });
                                                        }
                                                    } else if (location.pathname.includes('api')) {
                                                        const res = await getServerPlanTemplateList(
                                                            {
                                                                moduleId: currentSpace?.id,
                                                                // moduleId: 139,
                                                                pageSize: parseInt(
                                                                    (window.innerHeight - 110) /
                                                                        30 -
                                                                        1,
                                                                    10
                                                                ),
                                                                pageIndex:
                                                                    currentPageForPlanList || 1
                                                            }
                                                        );
                                                        setPlanTemplateList(res?.planTemplateList);
                                                        // planList = res?.planList;
                                                        // if (+query?.planId) {
                                                        //     planInfo = await getStabilityDetail({
                                                        //         planId: +query?.planId
                                                        //     });
                                                        //     const planResume =
                                                        //         await getStabilityResume({
                                                        //             planId: +query?.planId
                                                        //         });
                                                        //     setCurrentPlan({
                                                        //         id: currentPlan?.planId,
                                                        //         planId: currentPlan?.planId,
                                                        //         ...planInfo,
                                                        //         ...planResume
                                                        //     });
                                                        // }
                                                    } else {
                                                        const res = await getPlanList({
                                                            moduleId: currentSpace?.id,
                                                            pageSize: parseInt(
                                                                (window.innerHeight - 110) / 30,
                                                                10
                                                            ),
                                                            planType: currentPlanGroup?.planType,
                                                            pageIndex: currentPageForPlanList
                                                        });
                                                        planList = res?.planList;
                                                        planInfo = await getPlanDetail({
                                                            planIdList: [currentPlan?.id]
                                                        });
                                                        setCurrentPlan({
                                                            id: currentPlan?.planId,
                                                            planId: currentPlan?.planId,
                                                            ...planInfo?.planList?.[0]
                                                        });
                                                    }
                                                    setPlanList(planList);
                                                    setLoading(false);
                                                }
                                            } catch (err) {
                                                setLoading(false);
                                            }
                                        }}
                                    />
                                </span>
                            </div>
                            {(3 !== currentPlanGroup?.planType ||
                                (isElectron() && 3 === currentPlanGroup?.planType)) &&
                                !location.pathname.includes('stability') && (
                                    <div className={classnames(styles.iconWrapper)}>
                                        <span className={styles.addIcon}>
                                            <PlusOutlined
                                                onClick={() => {
                                                    const createPath = location.pathname.includes(
                                                        'daily'
                                                    )
                                                        ? '/daily/create'
                                                        : location.pathname.includes('api')
                                                        ? '/api/create'
                                                        : '/create';

                                                    navigate(
                                                        stringifyUrl({
                                                            url: '/' + currentModule + createPath,
                                                            query: {
                                                                moduleId: currentSpace?.id,
                                                                planType: query?.planType ?? 1,
                                                                'filters[planId]':
                                                                    query?.['filters[planId]']
                                                            }
                                                        })
                                                    );
                                                }}
                                            />
                                        </span>
                                    </div>
                                )}
                        </div>
                    </div>
                </div>
                <Spin spinning={loading}>
                    {location.pathname.includes('daily') ? (
                        <DailyDirectoryPlanList
                            searchValue={searchValue}
                            openKeys={openKeys}
                            loading={loading}
                            setLoading={setLoading}
                            setOpenKeys={setOpenKeys}
                        />
                    ) : location.pathname.includes('stability') ? (
                        <StabilityDirectoryPlanList
                            searchValue={searchValue}
                            loading={loading}
                            setLoading={setLoading}
                            setSearchValue={setSearchValue}
                            ref={stabilityDirectoryRef}
                        />
                    ) : location.pathname.includes('api') ? (
                        <ApiDirectoryPlanList
                            searchValue={searchValue}
                            openKeys={openKeys}
                            loading={loading}
                            setLoading={setLoading}
                            setOpenKeys={setOpenKeys}
                        />
                    ) : (
                        <DirectoryPlanList
                            searchValue={searchValue}
                            openKeys={openKeys}
                            loading={loading}
                            setLoading={setLoading}
                            setOpenKeys={setOpenKeys}
                        />
                    )}
                </Spin>
            </div>
        </>
    );
}

export default connectModel([baseModel, planModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentModule: state.common.base.currentModule,
    currentPlan: state.common.plan.currentPlan,
    planList: state.common.plan.planList,
    planGroupList: state.common.plan.planGroupList,
    currentPlanGroup: state.common.plan.currentPlanGroup,
    currentPageForPlanList: state.common.plan.currentPageForPlanList,
    planTemplateList: state.common.plan.planTemplateList
}))(LayoutSider);
